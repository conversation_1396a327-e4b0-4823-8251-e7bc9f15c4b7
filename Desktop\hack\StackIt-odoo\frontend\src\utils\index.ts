/**
 * API Services Index
 * Central export point for all API services
 */

// Export base API utilities
export { apiClient, TokenManager, checkApiHealth } from './api';
export type { ApiError, ApiResponse } from './api';

// Export authentication API
export { authApi } from './authApi';
export type { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  UserResponse 
} from './authApi';

// Export questions API
export { questionsApi } from './questionsApi';
export type { 
  QuestionCreateRequest,
  QuestionResponse,
  QuestionListItem,
  QuestionListResponse,
  QuestionFilters,
  AuthorInfo,
  TagInfo
} from './questionsApi';

// Export answers API
export { answersApi } from './answersApi';
export type { 
  AnswerCreateRequest,
  AnswerResponse,
  AcceptAnswerResponse,
  AnswerAuthorInfo
} from './answersApi';

// Export votes API
export { votesApi } from './votesApi';
export type { 
  VoteCreateRequest,
  VoteResponse,
  VoteRemoveResponse
} from './votesApi';

// Export tags API
export { tagsApi } from './tagsApi';
export type { 
  TagResponse,
  TagListResponse
} from './tagsApi';

// Export existing utilities
export { cn } from './cn';
