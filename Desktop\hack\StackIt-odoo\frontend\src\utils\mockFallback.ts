/**
 * Mock Data Fallback
 * Provides mock data when the backend API is not available
 */

import { mockQuestions } from '@/data/mockQuestions';
import { mockTags } from '@/data/mockTags';
import { mockUsers } from '@/data/mockUsers';
import { Question, Tag, User } from '@/types';

// Convert mock data to API format
export const convertMockQuestionToApi = (mockQuestion: any): any => {
  // Handle both string and number IDs
  const getId = (id: string | number) => {
    if (typeof id === 'number') return id;
    return parseInt(id.toString().replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000);
  };

  return {
    id: getId(mockQuestion.id),
    title: mockQuestion.title,
    description: mockQuestion.description,
    view_count: mockQuestion.view_count || mockQuestion.viewCount || 0,
    vote_score: mockQuestion.vote_score || 0,
    answer_count: mockQuestion.answer_count || mockQuestion.answerCount || 0,
    is_closed: mockQuestion.is_closed || false,
    has_accepted_answer: mockQuestion.has_accepted_answer || mockQuestion.hasAcceptedAnswer || false,
    author: {
      id: getId(mockQuestion.author.id),
      username: mockQuestion.author.username,
      full_name: mockQuestion.author.full_name,
      reputation_score: mockQuestion.author.reputation_score || mockQuestion.author.reputation || 0,
    },
    tags: mockQuestion.tags.map((tag: any) => ({
      id: getId(tag.id),
      name: tag.name,
      color: tag.color,
    })),
    created_at: mockQuestion.created_at || mockQuestion.createdAt || new Date().toISOString(),
    updated_at: mockQuestion.updated_at || mockQuestion.updatedAt || new Date().toISOString(),
  };
};

export const convertMockTagToApi = (mockTag: any): any => {
  const getId = (id: string | number) => {
    if (typeof id === 'number') return id;
    return parseInt(id.toString().replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000);
  };

  return {
    id: getId(mockTag.id),
    name: mockTag.name,
    description: mockTag.description,
    color: mockTag.color,
    usage_count: mockTag.usage_count || mockTag.questionCount || 0,
  };
};

export const convertMockUserToApi = (mockUser: any): any => {
  const getId = (id: string | number) => {
    if (typeof id === 'number') return id;
    return parseInt(id.toString().replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000);
  };

  return {
    id: getId(mockUser.id),
    username: mockUser.username,
    email: mockUser.email,
    full_name: mockUser.full_name,
    bio: mockUser.bio,
    is_active: !mockUser.banned,
    is_verified: mockUser.is_verified || false,
    role: mockUser.role,
    reputation_score: mockUser.reputation_score || mockUser.reputation || 0,
    questions_count: mockUser.questions_count || mockUser.questionsCount || 0,
    answers_count: mockUser.answers_count || mockUser.answersCount || 0,
    created_at: mockUser.created_at || mockUser.createdAt || new Date().toISOString(),
    updated_at: mockUser.updated_at || new Date().toISOString(),
  };
};

// Mock API responses
export const mockApiResponses = {
  getQuestions: (filters: any = {}) => {
    const questions = mockQuestions.map(convertMockQuestionToApi);
    
    // Apply basic filtering
    let filteredQuestions = [...questions];
    
    if (filters.search) {
      const query = filters.search.toLowerCase();
      filteredQuestions = filteredQuestions.filter(q =>
        q.title.toLowerCase().includes(query) ||
        q.description.toLowerCase().includes(query)
      );
    }
    
    if (filters.tags && filters.tags.length > 0) {
      filteredQuestions = filteredQuestions.filter(q =>
        q.tags.some((tag: any) => filters.tags.includes(tag.name))
      );
    }
    
    // Apply sorting
    if (filters.sort_by === 'oldest') {
      filteredQuestions.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    } else {
      filteredQuestions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    }
    
    // Apply pagination
    const page = filters.page || 1;
    const per_page = filters.per_page || 10;
    const start = (page - 1) * per_page;
    const end = start + per_page;
    const paginatedQuestions = filteredQuestions.slice(start, end);
    
    return {
      questions: paginatedQuestions,
      total: filteredQuestions.length,
      page,
      per_page,
      has_next: end < filteredQuestions.length,
      has_prev: page > 1,
    };
  },

  getQuestionById: (id: number) => {
    const getId = (questionId: string | number) => {
      if (typeof questionId === 'number') return questionId;
      return parseInt(questionId.toString().replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000);
    };

    const question = mockQuestions.find(q => getId(q.id) === id);
    if (!question) {
      throw { detail: 'Question not found', status_code: 404 };
    }
    return convertMockQuestionToApi(question);
  },

  getTags: () => {
    const tags = mockTags.map(convertMockTagToApi);
    return {
      tags,
      total: tags.length,
    };
  },

  login: (credentials: any) => {
    const user = mockUsers.find(u => u.username === credentials.username || u.email === credentials.username);
    if (!user) {
      throw { detail: 'Invalid username or password', status_code: 401 };
    }
    
    return {
      user: convertMockUserToApi(user),
      token: {
        access_token: 'mock_token_' + Date.now(),
        token_type: 'bearer',
        expires_in: 1800,
      },
      message: 'Login successful',
    };
  },

  register: (userData: any) => {
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.username === userData.username || u.email === userData.email);
    if (existingUser) {
      throw { detail: 'Username or email already exists', status_code: 400 };
    }
    
    const newUser = {
      id: `user_${Date.now()}`,
      username: userData.username,
      email: userData.email,
      full_name: userData.full_name,
      bio: userData.bio,
      role: 'USER',
      reputation: 0,
      questionsCount: 0,
      answersCount: 0,
      createdAt: new Date().toISOString(),
      banned: false,
    };
    
    mockUsers.push(newUser);
    
    return {
      user: convertMockUserToApi(newUser),
      token: {
        access_token: 'mock_token_' + Date.now(),
        token_type: 'bearer',
        expires_in: 1800,
      },
      message: 'Registration successful',
    };
  },

  getCurrentUser: () => {
    // Return the first non-banned user as current user for demo
    const user = mockUsers.find(u => !u.banned);
    if (!user) {
      throw { detail: 'User not found', status_code: 404 };
    }
    return convertMockUserToApi(user);
  },
};
