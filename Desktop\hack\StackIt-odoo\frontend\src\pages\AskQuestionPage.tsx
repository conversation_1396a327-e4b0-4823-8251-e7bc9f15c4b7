import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useAppStore } from '@/store/useAppStore';
import { QuestionForm } from '@/components/QuestionForm';

export function AskQuestionPage() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addQuestion } = useAppStore();

  const handleSubmit = (data: any) => {
    if (!user) return;

    const questionData = {
      ...data,
      authorId: user.id,
      author: user,
      status: user.role === 'ADMIN' ? 'APPROVED' : 'PENDING' as const,
    };

    addQuestion(questionData);
    navigate('/');
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="card">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">Ask a Question</h1>
          <p className="text-gray-600 mt-2">
            Get help from the community by asking a clear, detailed question.
          </p>
        </div>
        <div className="p-6">
          <QuestionForm onSubmit={handleSubmit} />
        </div>
      </div>
    </div>
  );
}
