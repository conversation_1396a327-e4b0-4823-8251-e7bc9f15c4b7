import React, { useState } from 'react';
import { Search, Tag as TagIcon } from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';
import { TagFilter } from '@/components/TagFilter';

export function TagsPage() {
  const { tags, searchTags } = useAppStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'popularity'>('popularity');

  const filteredTags = searchQuery 
    ? searchTags(searchQuery)
    : tags;

  const sortedTags = [...filteredTags].sort((a, b) => {
    if (sortBy === 'popularity') {
      return b.questionCount - a.questionCount;
    }
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Tags</h1>
        <p className="text-gray-600">
          Browse questions by topic. Tags help categorize and organize content.
        </p>
      </div>

      {/* Search and Filter */}
      <div className="card p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search tags..."
              className="input pl-10"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'popularity')}
              className="input text-sm py-1 px-2 w-auto"
            >
              <option value="popularity">Popularity</option>
              <option value="name">Name</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tags Grid */}
      {sortedTags.length === 0 ? (
        <div className="text-center py-12">
          <TagIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No tags found</h3>
          <p className="text-gray-600">
            {searchQuery ? `No tags match "${searchQuery}"` : 'No tags available yet.'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {sortedTags.map((tag) => (
              <div
                key={tag.id}
                className="card p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 truncate">
                    {tag.name}
                  </h3>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {tag.questionCount}
                  </span>
                </div>
                
                {tag.description && (
                  <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                    {tag.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between">
                  <TagFilter tag={tag} />
                  <span className="text-xs text-gray-500">
                    {tag.questionCount} question{tag.questionCount !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center text-sm text-gray-500">
            Showing {sortedTags.length} tag{sortedTags.length !== 1 ? 's' : ''}
            {searchQuery && ` matching "${searchQuery}"`}
          </div>
        </>
      )}
    </div>
  );
}
