import { Question } from '@/types';
import { mockUsers } from './mockUsers';
import { mockTags } from './mockTags';

export const mockQuestions: Question[] = [
  {
    id: '1',
    title: 'How to implement authentication in React with TypeScript?',
    description: `<p>I'm building a React application with TypeScript and need to implement user authentication. I want to use JWT tokens and store them securely.</p>

<p>Here's what I've tried so far:</p>

<pre><code>const AuthContext = createContext&lt;AuthContextType | null&gt;(null);

export const useAuth = () =&gt; {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};</code></pre>

<p>But I'm not sure about the best practices for:</p>
<ul>
<li>Token storage (localStorage vs httpOnly cookies)</li>
<li>Automatic token refresh</li>
<li>Route protection</li>
</ul>

<p>What's the recommended approach for a production application?</p>`,
    authorId: '2',
    author: mockUsers[1],
    status: 'APPROVED',
    tags: [mockTags[0], mockTags[1], mockTags[2]], // react, typescript, javascript
    createdAt: '2023-11-15T10:30:00Z',
    updatedAt: '2023-11-15T10:30:00Z',
    viewCount: 245,
    answerCount: 3,
    hasAcceptedAnswer: true,
  },
  {
    id: '2',
    title: 'Best practices for state management in large React applications',
    description: `<p>I'm working on a large React application with multiple teams, and we're struggling with state management. We're currently using a mix of useState, useContext, and some Redux, but it's becoming messy.</p>

<p><strong>Current issues:</strong></p>
<ul>
<li>State is scattered across components</li>
<li>Prop drilling is getting out of hand</li>
<li>Performance issues with unnecessary re-renders</li>
<li>Difficult to debug state changes</li>
</ul>

<p>We're considering:</p>
<ol>
<li>Migrating everything to Redux Toolkit</li>
<li>Using Zustand for simpler state management</li>
<li>Implementing a custom context-based solution</li>
</ol>

<p>What would you recommend for a team of 15+ developers working on the same codebase?</p>`,
    authorId: '3',
    author: mockUsers[2],
    status: 'APPROVED',
    tags: [mockTags[0], mockTags[18]], // react, redux
    createdAt: '2023-11-14T14:20:00Z',
    updatedAt: '2023-11-14T14:20:00Z',
    viewCount: 189,
    answerCount: 5,
    hasAcceptedAnswer: false,
  },
  {
    id: '3',
    title: 'Docker container not starting - Node.js application',
    description: `<p>I'm trying to containerize my Node.js application, but the Docker container keeps failing to start. Here's my Dockerfile:</p>

<pre><code>FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]</code></pre>

<p>The error I'm getting is:</p>
<pre><code>Error: Cannot find module '/app/dist/index.js'
    at Function.Module._resolveFilename (internal/modules/cjs/loader.js:815:15)</code></pre>

<p>My package.json start script is: <code>"start": "node dist/index.js"</code></p>

<p>The application works fine locally, but fails in Docker. What am I missing?</p>`,
    authorId: '4',
    author: mockUsers[3],
    status: 'APPROVED',
    tags: [mockTags[3], mockTags[12]], // node.js, docker
    createdAt: '2023-11-13T09:45:00Z',
    updatedAt: '2023-11-13T09:45:00Z',
    viewCount: 156,
    answerCount: 2,
    hasAcceptedAnswer: true,
  },
  {
    id: '4',
    title: 'CSS Grid vs Flexbox: When to use which?',
    description: `<p>I'm always confused about when to use CSS Grid versus Flexbox. I understand the basics of both, but I struggle with choosing the right tool for the job.</p>

<p><strong>What I know:</strong></p>
<ul>
<li>Flexbox is one-dimensional (row or column)</li>
<li>Grid is two-dimensional (rows and columns)</li>
<li>Both can be used for responsive layouts</li>
</ul>

<p><strong>Specific scenarios I'm unsure about:</strong></p>
<ol>
<li>Card layouts with varying content heights</li>
<li>Navigation bars with multiple sections</li>
<li>Complex dashboard layouts</li>
<li>Mobile-first responsive design</li>
</ol>

<p>Are there any performance differences? Can you provide some practical examples of when to choose one over the other?</p>`,
    authorId: '5',
    author: mockUsers[4],
    status: 'APPROVED',
    tags: [mockTags[5]], // css
    createdAt: '2023-11-12T16:15:00Z',
    updatedAt: '2023-11-12T16:15:00Z',
    viewCount: 298,
    answerCount: 4,
    hasAcceptedAnswer: false,
  },
  {
    id: '5',
    title: 'How to optimize React app performance with large datasets?',
    description: `<p>I have a React application that displays large datasets (10,000+ items) in tables and lists. The app is becoming very slow, especially on mobile devices.</p>

<p><strong>Current implementation:</strong></p>
<pre><code>const DataTable = ({ data }) =&gt; {
  return (
    &lt;table&gt;
      &lt;tbody&gt;
        {data.map(item =&gt; (
          &lt;TableRow key={item.id} item={item} /&gt;
        ))}
      &lt;/tbody&gt;
    &lt;/table&gt;
  );
};</code></pre>

<p><strong>Performance issues:</strong></p>
<ul>
<li>Initial render takes 3-4 seconds</li>
<li>Scrolling is janky</li>
<li>Filtering/sorting is slow</li>
<li>Memory usage keeps increasing</li>
</ul>

<p>I've heard about virtualization, but I'm not sure how to implement it properly. Should I use react-window, react-virtualized, or build a custom solution?</p>`,
    authorId: '6',
    author: mockUsers[5],
    status: 'APPROVED',
    tags: [mockTags[0], mockTags[1]], // react, typescript
    createdAt: '2023-11-11T11:30:00Z',
    updatedAt: '2023-11-11T11:30:00Z',
    viewCount: 167,
    answerCount: 3,
    hasAcceptedAnswer: true,
  },
  {
    id: '6',
    title: 'Python vs JavaScript for backend development in 2024',
    description: `<p>I'm starting a new project and need to choose between Python and JavaScript (Node.js) for the backend. The project will involve:</p>

<ul>
<li>REST API development</li>
<li>Real-time features (WebSocket)</li>
<li>Database operations (PostgreSQL)</li>
<li>File processing and uploads</li>
<li>Integration with third-party APIs</li>
</ul>

<p><strong>Team context:</strong></p>
<ul>
<li>Frontend is React/TypeScript</li>
<li>Team has experience with both languages</li>
<li>Performance and scalability are important</li>
<li>Development speed matters</li>
</ul>

<p>What are the pros and cons of each approach in 2024? Any recent developments that might influence the decision?</p>`,
    authorId: '7',
    author: mockUsers[6],
    status: 'APPROVED',
    tags: [mockTags[4], mockTags[3], mockTags[2]], // python, node.js, javascript
    createdAt: '2023-11-10T13:45:00Z',
    updatedAt: '2023-11-10T13:45:00Z',
    viewCount: 234,
    answerCount: 6,
    hasAcceptedAnswer: false,
  },
  {
    id: '7',
    title: 'How to handle file uploads in Next.js with TypeScript?',
    description: `<p>I need to implement file upload functionality in my Next.js application. Users should be able to upload images and documents with the following requirements:</p>

<ul>
<li>Multiple file selection</li>
<li>File type validation (images: jpg, png, gif; documents: pdf, docx)</li>
<li>File size limits (max 10MB per file)</li>
<li>Progress indication during upload</li>
<li>Preview for images</li>
</ul>

<p>I'm using the app router and want to handle uploads on the server side. Here's what I have so far:</p>

<pre><code>// app/api/upload/route.ts
export async function POST(request: Request) {
  const formData = await request.formData();
  const file = formData.get('file') as File;

  if (!file) {
    return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
  }

  // What's the best way to handle the file here?
}</code></pre>

<p>Should I use a cloud service like AWS S3 or store files locally? How do I implement proper validation and error handling?</p>`,
    authorId: '8',
    author: mockUsers[7],
    status: 'APPROVED',
    tags: [mockTags[16], mockTags[1]], // next.js, typescript
    createdAt: '2023-11-09T08:20:00Z',
    updatedAt: '2023-11-09T08:20:00Z',
    viewCount: 145,
    answerCount: 2,
    hasAcceptedAnswer: false,
  },
];
