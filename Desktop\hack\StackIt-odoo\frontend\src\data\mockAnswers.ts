import { Answer } from '@/types';
import { mockUsers } from './mockUsers';

export const mockAnswers: Answer[] = [
  {
    id: '1',
    content: `<p>For authentication in React with TypeScript, I recommend using <strong>httpOnly cookies</strong> for token storage rather than localStorage for better security. Here's a comprehensive approach:</p>

<h3>1. Token Storage</h3>
<pre><code>// Use httpOnly cookies to prevent XSS attacks
// Set cookies on the server side after successful login
res.cookie('accessToken', token, {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 15 * 60 * 1000 // 15 minutes
});</code></pre>

<h3>2. Automatic Token Refresh</h3>
<pre><code>// Implement a refresh token mechanism
const useTokenRefresh = () => {
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        await fetch('/api/auth/refresh', { 
          method: 'POST',
          credentials: 'include' 
        });
      } catch (error) {
        // Handle refresh failure - redirect to login
        logout();
      }
    }, 14 * 60 * 1000); // Refresh every 14 minutes

    return () => clearInterval(interval);
  }, []);
};</code></pre>

<h3>3. Route Protection</h3>
<pre><code>// Create a ProtectedRoute component
const ProtectedRoute: React.FC&lt;{ children: React.ReactNode }&gt; = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) return &lt;LoadingSpinner /&gt;;
  if (!user) return &lt;Navigate to="/login" replace /&gt;;
  
  return &lt;&gt;{children}&lt;/&gt;;
};</code></pre>

<p>This approach provides better security and user experience. The httpOnly cookies prevent XSS attacks, and automatic refresh keeps users logged in seamlessly.</p>`,
    questionId: '1',
    authorId: '1',
    author: mockUsers[0],
    isAccepted: true,
    status: 'APPROVED',
    createdAt: '2023-11-15T11:15:00Z',
    updatedAt: '2023-11-15T11:15:00Z',
    upvotes: 15,
    downvotes: 1,
    score: 14,
  },
  {
    id: '2',
    content: `<p>I'd also add that you should consider using a library like <code>@tanstack/react-query</code> for handling authentication state and API calls. It provides excellent caching and synchronization features.</p>

<pre><code>const useAuthQuery = () => {
  return useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => fetch('/api/auth/me').then(res => res.json()),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });
};</code></pre>

<p>This approach automatically handles loading states, error states, and keeps your auth state in sync across components.</p>`,
    questionId: '1',
    authorId: '3',
    author: mockUsers[2],
    isAccepted: false,
    status: 'APPROVED',
    createdAt: '2023-11-15T12:30:00Z',
    updatedAt: '2023-11-15T12:30:00Z',
    upvotes: 8,
    downvotes: 0,
    score: 8,
  },
  {
    id: '3',
    content: `<p>For large React applications, I strongly recommend <strong>Zustand</strong> over Redux Toolkit. Here's why:</p>

<h3>Zustand Benefits:</h3>
<ul>
<li>Much smaller bundle size (~2.5kb vs ~20kb for RTK)</li>
<li>Less boilerplate code</li>
<li>Better TypeScript support out of the box</li>
<li>Easier to learn for new team members</li>
</ul>

<h3>Example Zustand Store:</h3>
<pre><code>interface AppState {
  user: User | null;
  questions: Question[];
  setUser: (user: User | null) => void;
  addQuestion: (question: Question) => void;
}

const useAppStore = create&lt;AppState&gt;((set) => ({
  user: null,
  questions: [],
  setUser: (user) => set({ user }),
  addQuestion: (question) => set((state) => ({ 
    questions: [...state.questions, question] 
  })),
}));</code></pre>

<h3>Performance Optimization:</h3>
<pre><code>// Use selectors to prevent unnecessary re-renders
const user = useAppStore((state) => state.user);
const questionsCount = useAppStore((state) => state.questions.length);</code></pre>

<p>For a team of 15+ developers, Zustand's simplicity will reduce onboarding time and maintenance overhead significantly.</p>`,
    questionId: '2',
    authorId: '1',
    author: mockUsers[0],
    isAccepted: false,
    status: 'APPROVED',
    createdAt: '2023-11-14T15:45:00Z',
    updatedAt: '2023-11-14T15:45:00Z',
    upvotes: 22,
    downvotes: 3,
    score: 19,
  },
  {
    id: '4',
    content: `<p>The issue is that you're not building your TypeScript files in the Docker container. Your Dockerfile copies the source files but doesn't compile them.</p>

<p>Here's the corrected Dockerfile:</p>

<pre><code>FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including devDependencies for building)
RUN npm ci

# Copy source code
COPY src ./src

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

EXPOSE 3000

CMD ["npm", "start"]</code></pre>

<p><strong>Key changes:</strong></p>
<ul>
<li>Multi-stage build to separate build and runtime environments</li>
<li>Install devDependencies in builder stage for TypeScript compilation</li>
<li>Copy built files from builder to production stage</li>
<li>Only production dependencies in final image</li>
</ul>

<p>Make sure your <code>tsconfig.json</code> has the correct <code>outDir</code> set to <code>"dist"</code>.</p>`,
    questionId: '3',
    authorId: '4',
    author: mockUsers[3],
    isAccepted: true,
    status: 'APPROVED',
    createdAt: '2023-11-13T10:20:00Z',
    updatedAt: '2023-11-13T10:20:00Z',
    upvotes: 18,
    downvotes: 0,
    score: 18,
  },
  {
    id: '5',
    content: `<p>Great question! Here's a practical guide for choosing between CSS Grid and Flexbox:</p>

<h3>Use Flexbox when:</h3>
<ul>
<li><strong>One-dimensional layouts</strong> (either row or column)</li>
<li><strong>Navigation bars</strong> - perfect for distributing space between nav items</li>
<li><strong>Centering content</strong> - both horizontally and vertically</li>
<li><strong>Equal height cards</strong> in a row</li>
</ul>

<h3>Use CSS Grid when:</h3>
<ul>
<li><strong>Two-dimensional layouts</strong> (rows AND columns)</li>
<li><strong>Complex dashboard layouts</strong> with multiple sections</li>
<li><strong>Magazine-style layouts</strong> with overlapping elements</li>
<li><strong>Responsive layouts</strong> that need to change structure at different breakpoints</li>
</ul>

<h3>Practical Examples:</h3>

<h4>Navigation Bar (Flexbox):</h4>
<pre><code>.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-links {
  display: flex;
  gap: 1rem;
}</code></pre>

<h4>Card Grid (CSS Grid):</h4>
<pre><code>.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}</code></pre>

<h4>Dashboard Layout (CSS Grid):</h4>
<pre><code>.dashboard {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 250px 1fr 200px;
}</code></pre>

<p><strong>Performance:</strong> Both are highly optimized by browsers. Grid might have a slight edge for complex layouts as it reduces the need for nested containers.</p>

<p><strong>Mobile-first tip:</strong> Start with Flexbox for simple stacking, then enhance with Grid for larger screens using media queries.</p>`,
    questionId: '4',
    authorId: '5',
    author: mockUsers[4],
    isAccepted: false,
    status: 'APPROVED',
    createdAt: '2023-11-12T17:30:00Z',
    updatedAt: '2023-11-12T17:30:00Z',
    upvotes: 25,
    downvotes: 1,
    score: 24,
  },
];
