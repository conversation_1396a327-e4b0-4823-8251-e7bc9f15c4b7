import { Notification } from '@/types';

export const mockNotifications: Notification[] = [
  {
    id: '1',
    userId: '2',
    type: 'ANSWER_RECEIVED',
    title: 'New answer to your question',
    message: 'admin answered your question "How to implement authentication in React with TypeScript?"',
    read: false,
    data: {
      questionId: '1',
      answerId: '1',
      answerAuthor: 'admin'
    },
    createdAt: '2023-11-15T11:15:00Z',
  },
  {
    id: '2',
    userId: '2',
    type: 'ANSWER_ACCEPTED',
    title: 'Your answer was accepted',
    message: 'john_doe accepted your answer to "Best practices for state management in large React applications"',
    read: false,
    data: {
      questionId: '2',
      answerId: '3',
      acceptedBy: 'john_doe'
    },
    createdAt: '2023-11-14T16:20:00Z',
  },
  {
    id: '3',
    userId: '3',
    type: 'MENTION_RECEIVED',
    title: 'You were mentioned',
    message: 'jane_smith mentioned you in an answer to "Docker container not starting"',
    read: true,
    data: {
      questionId: '3',
      answerId: '4',
      mentionedBy: 'jane_smith'
    },
    createdAt: '2023-11-13T14:45:00Z',
  },
  {
    id: '4',
    userId: '2',
    type: 'ADMIN_MESSAGE',
    title: 'Platform Update',
    message: 'New features have been added to StackIt! Check out the improved rich text editor and notification system.',
    read: true,
    data: {
      type: 'feature_announcement',
      version: '2.1.0'
    },
    createdAt: '2023-11-10T09:00:00Z',
  },
  {
    id: '5',
    userId: '4',
    type: 'ANSWER_RECEIVED',
    title: 'New answer to your question',
    message: 'mike_wilson answered your question "Docker container not starting - Node.js application"',
    read: true,
    data: {
      questionId: '3',
      answerId: '4',
      answerAuthor: 'mike_wilson'
    },
    createdAt: '2023-11-13T10:20:00Z',
  },
  {
    id: '6',
    userId: '5',
    type: 'ANSWER_RECEIVED',
    title: 'New answer to your question',
    message: 'sarah_johnson answered your question "CSS Grid vs Flexbox: When to use which?"',
    read: true,
    data: {
      questionId: '4',
      answerId: '5',
      answerAuthor: 'sarah_johnson'
    },
    createdAt: '2023-11-12T17:30:00Z',
  },
  {
    id: '7',
    userId: '6',
    type: 'ANSWER_ACCEPTED',
    title: 'Your answer was accepted',
    message: 'alex_chen accepted your answer to "How to optimize React app performance with large datasets?"',
    read: true,
    data: {
      questionId: '5',
      answerId: '6',
      acceptedBy: 'alex_chen'
    },
    createdAt: '2023-11-11T15:45:00Z',
  },
];
