/**
 * Answers Store - API-based state management for answers
 */

import { create } from 'zustand';
import { Answer } from '@/types';
import { answersApi, votesApi, ApiError } from '@/utils';

interface AnswersState {
  // Data
  answersByQuestion: Record<number, Answer[]>;
  
  // UI State
  loading: boolean;
  error: string | null;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Answer actions
  getAnswersByQuestionId: (questionId: number) => Answer[];
  createAnswer: (answerData: { content: string; question_id: number }) => Promise<Answer | null>;
  acceptAnswer: (answerId: number) => Promise<boolean>;
  voteOnAnswer: (answerId: number, isUpvote: boolean) => Promise<boolean>;
  removeVote: (answerId: number) => Promise<boolean>;
  
  // Utility actions
  reset: () => void;
}

// Helper function to convert backend answer to frontend format
const convertBackendAnswer = (backendAnswer: any): Answer => {
  return {
    id: backendAnswer.id,
    content: backendAnswer.content,
    question_id: backendAnswer.question_id,
    author_id: backendAnswer.author?.id,
    author: {
      id: backendAnswer.author.id,
      username: backendAnswer.author.username,
      email: backendAnswer.author.email || '',
      full_name: backendAnswer.author.full_name,
      bio: backendAnswer.author.bio,
      role: 'USER',
      is_active: true,
      is_verified: false,
      reputation_score: backendAnswer.author.reputation_score,
      questions_count: 0,
      answers_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    is_accepted: backendAnswer.is_accepted,
    created_at: backendAnswer.created_at,
    updated_at: backendAnswer.updated_at,
    upvotes: backendAnswer.upvotes || 0,
    downvotes: backendAnswer.downvotes || 0,
    vote_score: backendAnswer.vote_score || 0,
  };
};

export const useAnswersStore = create<AnswersState>((set, get) => ({
  // Initial state
  answersByQuestion: {},
  loading: false,
  error: null,

  // UI Actions
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),

  // Answer actions
  getAnswersByQuestionId: (questionId: number) => {
    const { answersByQuestion } = get();
    return answersByQuestion[questionId] || [];
  },

  createAnswer: async (answerData) => {
    set({ loading: true, error: null });
    
    try {
      const response = await answersApi.createAnswer(answerData);
      const answer = convertBackendAnswer(response);
      
      // Add the new answer to the question's answers
      set(state => ({
        answersByQuestion: {
          ...state.answersByQuestion,
          [answerData.question_id]: [
            ...(state.answersByQuestion[answerData.question_id] || []),
            answer
          ]
        },
        loading: false,
      }));
      
      return answer;
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to create answer',
        loading: false,
      });
      return null;
    }
  },

  acceptAnswer: async (answerId: number) => {
    set({ loading: true, error: null });
    
    try {
      await answersApi.acceptAnswer(answerId);
      
      // Update the answer's accepted status
      set(state => {
        const newAnswersByQuestion = { ...state.answersByQuestion };
        
        // Find and update the answer
        Object.keys(newAnswersByQuestion).forEach(questionId => {
          newAnswersByQuestion[parseInt(questionId)] = newAnswersByQuestion[parseInt(questionId)].map(answer => ({
            ...answer,
            is_accepted: answer.id === answerId
          }));
        });
        
        return {
          answersByQuestion: newAnswersByQuestion,
          loading: false,
        };
      });
      
      return true;
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to accept answer',
        loading: false,
      });
      return false;
    }
  },

  voteOnAnswer: async (answerId: number, isUpvote: boolean) => {
    try {
      const response = await votesApi.voteOnAnswer(answerId, isUpvote);
      
      // Update the answer's vote counts
      set(state => {
        const newAnswersByQuestion = { ...state.answersByQuestion };
        
        Object.keys(newAnswersByQuestion).forEach(questionId => {
          newAnswersByQuestion[parseInt(questionId)] = newAnswersByQuestion[parseInt(questionId)].map(answer => 
            answer.id === answerId 
              ? {
                  ...answer,
                  upvotes: response.upvotes,
                  downvotes: response.downvotes,
                  vote_score: response.new_score,
                  user_vote: response.vote_type === 'UPVOTE' ? 'UP' : 'DOWN'
                }
              : answer
          );
        });
        
        return { answersByQuestion: newAnswersByQuestion };
      });
      
      return true;
    } catch (error) {
      const apiError = error as ApiError;
      set({ error: apiError.detail || 'Failed to vote on answer' });
      return false;
    }
  },

  removeVote: async (answerId: number) => {
    try {
      const response = await votesApi.removeVote(answerId);
      
      // Update the answer's vote counts
      set(state => {
        const newAnswersByQuestion = { ...state.answersByQuestion };
        
        Object.keys(newAnswersByQuestion).forEach(questionId => {
          newAnswersByQuestion[parseInt(questionId)] = newAnswersByQuestion[parseInt(questionId)].map(answer => 
            answer.id === answerId 
              ? {
                  ...answer,
                  upvotes: response.upvotes,
                  downvotes: response.downvotes,
                  vote_score: response.new_score,
                  user_vote: undefined
                }
              : answer
          );
        });
        
        return { answersByQuestion: newAnswersByQuestion };
      });
      
      return true;
    } catch (error) {
      const apiError = error as ApiError;
      set({ error: apiError.detail || 'Failed to remove vote' });
      return false;
    }
  },

  reset: () => set({
    answersByQuestion: {},
    loading: false,
    error: null,
  }),
}));
