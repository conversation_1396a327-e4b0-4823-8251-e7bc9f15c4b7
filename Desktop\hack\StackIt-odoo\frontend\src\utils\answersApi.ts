/**
 * Answers API Service
 * Handles all answer-related API calls
 */

import { apiClient } from './api';

// Types for answer requests and responses
export interface AnswerCreateRequest {
  content: string;
  question_id: number;
}

export interface AnswerAuthorInfo {
  id: number;
  username: string;
  full_name?: string;
  reputation_score: number;
}

export interface AnswerResponse {
  id: number;
  content: string;
  question_id: number;
  is_accepted: boolean;
  vote_score: number;
  upvotes: number;
  downvotes: number;
  author: AnswerAuthorInfo;
  created_at: string;
  updated_at: string;
}

export interface AcceptAnswerResponse {
  message: string;
  answer_id: number;
  question_id: number;
}

// Answers API functions
export const answersApi = {
  /**
   * Create a new answer for a question
   */
  async createAnswer(answerData: AnswerCreateRequest): Promise<AnswerResponse> {
    return apiClient.post<AnswerResponse>('/answers', answerData);
  },

  /**
   * Accept an answer (mark as accepted solution)
   */
  async acceptAnswer(answerId: number): Promise<AcceptAnswerResponse> {
    return apiClient.post<AcceptAnswerResponse>(`/answers/${answerId}/accept`);
  },

  /**
   * Update an answer (if needed in the future)
   */
  async updateAnswer(id: number, updates: { content: string }): Promise<AnswerResponse> {
    return apiClient.put<AnswerResponse>(`/answers/${id}`, updates);
  },

  /**
   * Delete an answer (if needed in the future)
   */
  async deleteAnswer(id: number): Promise<void> {
    return apiClient.delete<void>(`/answers/${id}`);
  },
};
