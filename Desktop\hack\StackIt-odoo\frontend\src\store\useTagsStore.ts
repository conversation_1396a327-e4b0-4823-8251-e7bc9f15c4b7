/**
 * Tags Store - API-based state management for tags
 */

import { create } from 'zustand';
import { Tag } from '@/types';
import { tagsApi, ApiError } from '@/utils';

interface TagsState {
  // Data
  tags: Tag[];
  
  // UI State
  loading: boolean;
  error: string | null;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Tag actions
  fetchTags: () => Promise<void>;
  searchTags: (query: string) => Tag[];
  getPopularTags: (limit?: number) => Tag[];
  
  // Utility actions
  reset: () => void;
}

// Helper function to convert backend tag to frontend format
const convertBackendTag = (backendTag: any): Tag => {
  return {
    id: backendTag.id,
    name: backendTag.name,
    description: backendTag.description,
    color: backendTag.color,
    usage_count: backendTag.usage_count,
    created_at: new Date().toISOString(),
  };
};

export const useTagsStore = create<TagsState>((set, get) => ({
  // Initial state
  tags: [],
  loading: false,
  error: null,

  // UI Actions
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),

  // Tag actions
  fetchTags: async () => {
    set({ loading: true, error: null });
    
    try {
      const response = await tagsApi.getTags();
      const tags = response.tags.map(convertBackendTag);
      
      set({
        tags,
        loading: false,
      });
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to fetch tags',
        loading: false,
      });
    }
  },

  searchTags: (query: string) => {
    const { tags } = get();
    return tags.filter(tag => 
      tag.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  getPopularTags: (limit = 10) => {
    const { tags } = get();
    return tags
      .sort((a, b) => b.usage_count - a.usage_count)
      .slice(0, limit);
  },

  reset: () => set({
    tags: [],
    loading: false,
    error: null,
  }),
}));
