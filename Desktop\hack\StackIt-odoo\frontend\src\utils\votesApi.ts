/**
 * Votes API Service
 * Handles all voting-related API calls
 */

import { apiClient } from './api';

// Types for vote requests and responses
export interface VoteCreateRequest {
  is_upvote: boolean;
}

export interface VoteResponse {
  message: string;
  answer_id: number;
  vote_type: 'UPVOTE' | 'DOWNVOTE';
  new_score: number;
  upvotes: number;
  downvotes: number;
}

export interface VoteRemoveResponse {
  message: string;
  answer_id: number;
  new_score: number;
  upvotes: number;
  downvotes: number;
}

// Votes API functions
export const votesApi = {
  /**
   * Vote on an answer (upvote or downvote)
   */
  async voteOnAnswer(answerId: number, isUpvote: boolean): Promise<VoteResponse> {
    const voteData: VoteCreateRequest = { is_upvote: isUpvote };
    return apiClient.post<VoteResponse>(`/answers/${answerId}/vote`, voteData);
  },

  /**
   * Remove vote from an answer
   */
  async removeVote(answerId: number): Promise<VoteRemoveResponse> {
    return apiClient.delete<VoteRemoveResponse>(`/answers/${answerId}/vote`);
  },
};
