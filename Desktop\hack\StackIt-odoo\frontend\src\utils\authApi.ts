/**
 * Authentication API Service
 * Handles all authentication-related API calls
 */

import { apiClient, TokenManager } from './api';
import { mockApiResponses } from './mockFallback';

// Types for authentication requests and responses
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  bio?: string;
}

export interface AuthResponse {
  user: {
    id: number;
    username: string;
    email: string;
    full_name?: string;
    bio?: string;
    is_active: boolean;
    is_verified: boolean;
    role: string;
    reputation_score: number;
    questions_count: number;
    answers_count: number;
    created_at: string;
    updated_at: string;
  };
  token: {
    access_token: string;
    token_type: string;
    expires_in: number;
  };
  message: string;
}

export interface UserResponse {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  bio?: string;
  is_active: boolean;
  is_verified: boolean;
  role: string;
  reputation_score: number;
  questions_count: number;
  answers_count: number;
  created_at: string;
  updated_at: string;
}

// Authentication API functions
export const authApi = {
  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/login', credentials);

      // Store the token
      if (response.token?.access_token) {
        TokenManager.setToken(response.token.access_token);
      }

      return response;
    } catch (error: any) {
      // Fallback to mock data if backend is not available
      if (error.status_code === 0) {
        console.warn('Backend not available, using mock data');
        const response = mockApiResponses.login(credentials);

        // Store the mock token
        if (response.token?.access_token) {
          TokenManager.setToken(response.token.access_token);
        }

        return response;
      }
      throw error;
    }
  },

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/register', userData);
    
    // Store the token
    if (response.token?.access_token) {
      TokenManager.setToken(response.token.access_token);
    }
    
    return response;
  },

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<UserResponse> {
    try {
      return await apiClient.get<UserResponse>('/me');
    } catch (error: any) {
      // Fallback to mock data if backend is not available
      if (error.status_code === 0) {
        console.warn('Backend not available, using mock data');
        return mockApiResponses.getCurrentUser();
      }
      throw error;
    }
  },

  /**
   * Logout user (client-side only)
   */
  logout(): void {
    TokenManager.removeToken();
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = TokenManager.getToken();
    return token !== null && !TokenManager.isTokenExpired(token);
  },
};
