import React from 'react';
import { X } from 'lucide-react';
import { TagProps } from '@/types';
import { useAppStore } from '@/store/useAppStore';

export function TagFilter({ tag, onClick, removable = false, onRemove }: TagProps) {
  const { selectedTags, setSelectedTags } = useAppStore();
  const isSelected = selectedTags.includes(tag.name);

  const handleClick = () => {
    if (onClick) {
      onClick(tag);
    } else {
      // Default behavior: toggle tag selection
      if (isSelected) {
        setSelectedTags(selectedTags.filter(t => t !== tag.name));
      } else {
        setSelectedTags([...selectedTags, tag.name]);
      }
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(tag);
    } else {
      setSelectedTags(selectedTags.filter(t => t !== tag.name));
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
        isSelected
          ? 'bg-primary-600 text-white shadow-md'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      style={{
        backgroundColor: isSelected ? undefined : tag.color ? `${tag.color}20` : undefined,
        borderColor: tag.color,
      }}
    >
      <span>{tag.name}</span>
      <span className="text-xs opacity-75">({tag.questionCount})</span>
      {removable && (
        <button
          onClick={handleRemove}
          className="ml-1 hover:bg-black hover:bg-opacity-20 rounded-full p-0.5"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </button>
  );
}
