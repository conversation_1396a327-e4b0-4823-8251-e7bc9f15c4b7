export type UserRole = 'GUEST' | 'USER' | 'ADMIN';

export type QuestionStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export type AnswerStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export type VoteType = 'UP' | 'DOWN';

export type NotificationType = 
  | 'ANSWER_RECEIVED' 
  | 'ANSWER_ACCEPTED' 
  | 'MENTION_RECEIVED' 
  | 'ADMIN_MESSAGE';

export interface User {
  id: string;
  email: string;
  username: string;
  role: UserRole;
  avatar?: string;
  bio?: string;
  createdAt: string;
  banned: boolean;
  banReason?: string;
  reputation: number;
  questionsCount: number;
  answersCount: number;
}

export interface Tag {
  id: string;
  name: string;
  description?: string;
  color?: string;
  questionCount: number;
  createdAt: string;
}

export interface Question {
  id: string;
  title: string;
  description: string;
  authorId: string;
  author: User;
  status: QuestionStatus;
  tags: Tag[];
  createdAt: string;
  updatedAt: string;
  viewCount: number;
  answerCount: number;
  hasAcceptedAnswer: boolean;
}

export interface Answer {
  id: string;
  content: string;
  questionId: string;
  authorId: string;
  author: User;
  isAccepted: boolean;
  status: AnswerStatus;
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  score: number;
  userVote?: VoteType;
}

export interface Vote {
  id: string;
  userId: string;
  answerId: string;
  type: VoteType;
  createdAt: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  data?: Record<string, any>;
  createdAt: string;
}

export interface Report {
  id: string;
  reporterId: string;
  reporter: User;
  targetType: 'QUESTION' | 'ANSWER' | 'USER';
  targetId: string;
  reason: string;
  status: 'PENDING' | 'RESOLVED' | 'DISMISSED';
  createdAt: string;
  resolvedAt?: string;
}

export interface QuestionFilters {
  tags?: string[];
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'most-answered' | 'most-voted';
  status?: QuestionStatus;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  data: T;
  pagination?: PaginationInfo;
  success: boolean;
  message?: string;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
}

export interface QuestionForm {
  title: string;
  description: string;
  tags: string[];
}

export interface AnswerForm {
  content: string;
}

// Context types
export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (data: RegisterForm) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
}

export interface AppContextType {
  questions: Question[];
  answers: Answer[];
  tags: Tag[];
  notifications: Notification[];
  users: User[];
  loading: boolean;
  error: string | null;
}

// Component props types
export interface QuestionCardProps {
  question: Question;
  showActions?: boolean;
}

export interface AnswerCardProps {
  answer: Answer;
  questionAuthorId: string;
  showActions?: boolean;
}

export interface TagProps {
  tag: Tag;
  onClick?: (tag: Tag) => void;
  removable?: boolean;
  onRemove?: (tag: Tag) => void;
}

export interface VoteButtonsProps {
  answerId: string;
  score: number;
  userVote?: VoteType;
  onVote: (type: VoteType) => void;
  disabled?: boolean;
}

export interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  maxLength?: number;
  minHeight?: string;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}
