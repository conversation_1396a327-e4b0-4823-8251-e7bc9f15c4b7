export type UserRole = 'GUEST' | 'USER' | 'ADMIN';

export type QuestionStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export type AnswerStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export type VoteType = 'UP' | 'DOWN';

export type NotificationType =
  | 'ANSWER_RECEIVED'
  | 'ANSWER_ACCEPTED'
  | 'MENTION_RECEIVED'
  | 'ADMIN_MESSAGE';

// Updated User interface to match backend schema
export interface User {
  id: number; // Changed from string to number to match backend
  email: string;
  username: string;
  full_name?: string; // Added to match backend
  bio?: string;
  role: UserRole;
  avatar?: string;
  is_active: boolean; // Added to match backend
  is_verified: boolean; // Added to match backend
  reputation_score: number; // Renamed from reputation to match backend
  questions_count: number; // Renamed from questionsCount to match backend
  answers_count: number; // Renamed from answersCount to match backend
  created_at: string; // Renamed from createdAt to match backend
  updated_at: string; // Added to match backend
  banned?: boolean; // Made optional for backward compatibility
  banReason?: string;
}

// Updated Tag interface to match backend schema
export interface Tag {
  id: number; // Changed from string to number to match backend
  name: string;
  description?: string;
  color?: string;
  usage_count: number; // Renamed from questionCount to match backend
  created_at?: string; // Renamed from createdAt and made optional
}

// Updated Question interface to match backend schema
export interface Question {
  id: number; // Changed from string to number to match backend
  title: string;
  description: string;
  author_id?: number; // Renamed from authorId to match backend
  author: User;
  status?: QuestionStatus; // Made optional as backend doesn't always include this
  tags: Tag[];
  created_at: string; // Renamed from createdAt to match backend
  updated_at: string; // Renamed from updatedAt to match backend
  view_count: number; // Renamed from viewCount to match backend
  answer_count: number; // Renamed from answerCount to match backend
  has_accepted_answer: boolean; // Renamed from hasAcceptedAnswer to match backend
  vote_score?: number; // Added to match backend
  is_closed?: boolean; // Added to match backend
}

// Updated Answer interface to match backend schema
export interface Answer {
  id: number; // Changed from string to number to match backend
  content: string;
  question_id: number; // Renamed from questionId to match backend
  author_id?: number; // Renamed from authorId to match backend
  author: User;
  is_accepted: boolean; // Renamed from isAccepted to match backend
  status?: AnswerStatus; // Made optional as backend doesn't always include this
  created_at: string; // Renamed from createdAt to match backend
  updated_at: string; // Renamed from updatedAt to match backend
  upvotes: number;
  downvotes: number;
  vote_score: number; // Renamed from score to match backend
  user_vote?: VoteType; // Renamed from userVote to match backend
}

// Updated Vote interface to match backend schema
export interface Vote {
  id: number; // Changed from string to number to match backend
  user_id: number; // Renamed from userId to match backend
  answer_id: number; // Renamed from answerId to match backend
  type: VoteType;
  created_at: string; // Renamed from createdAt to match backend
}

// Updated Notification interface to match backend schema
export interface Notification {
  id: number; // Changed from string to number to match backend
  user_id: number; // Renamed from userId to match backend
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  data?: Record<string, any>;
  created_at: string; // Renamed from createdAt to match backend
}

// Updated Report interface to match backend schema
export interface Report {
  id: number; // Changed from string to number to match backend
  reporter_id: number; // Renamed from reporterId to match backend
  reporter: User;
  target_type: 'QUESTION' | 'ANSWER' | 'USER'; // Renamed from targetType to match backend
  target_id: number; // Renamed from targetId and changed to number to match backend
  reason: string;
  status: 'PENDING' | 'RESOLVED' | 'DISMISSED';
  created_at: string; // Renamed from createdAt to match backend
  resolved_at?: string; // Renamed from resolvedAt to match backend
}

export interface QuestionFilters {
  tags?: string[];
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'most-answered' | 'most-voted';
  status?: QuestionStatus;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  data: T;
  pagination?: PaginationInfo;
  success: boolean;
  message?: string;
}

// Form types - updated to match backend API requirements
export interface LoginForm {
  username: string; // Changed from email to username to match backend
  password: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  full_name?: string; // Added to match backend
  bio?: string; // Added to match backend
}

export interface QuestionForm {
  title: string;
  description: string;
  tag_names: string[]; // Renamed from tags to match backend API
}

export interface AnswerForm {
  content: string;
}

// Context types - updated to match new API
export interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>; // Changed parameter from email to username
  register: (data: RegisterForm) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  error: string | null; // Added error state
}

export interface AppContextType {
  questions: Question[];
  answers: Answer[];
  tags: Tag[];
  notifications: Notification[];
  users: User[];
  loading: boolean;
  error: string | null;
}

// Component props types
export interface QuestionCardProps {
  question: Question;
  showActions?: boolean;
}

export interface AnswerCardProps {
  answer: Answer;
  questionAuthorId: string;
  showActions?: boolean;
}

export interface TagProps {
  tag: Tag;
  onClick?: (tag: Tag) => void;
  removable?: boolean;
  onRemove?: (tag: Tag) => void;
}

export interface VoteButtonsProps {
  answerId: string;
  score: number;
  userVote?: VoteType;
  onVote: (type: VoteType) => void;
  disabled?: boolean;
}

export interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  maxLength?: number;
  minHeight?: string;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}
