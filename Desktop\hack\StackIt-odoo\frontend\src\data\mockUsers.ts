import { User } from '@/types';

export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    role: 'ADMI<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    bio: 'Platform administrator and full-stack developer with 10+ years of experience.',
    createdAt: '2023-01-15T10:00:00Z',
    banned: false,
    reputation: 5000,
    questionsCount: 25,
    answersCount: 150,
  },
  {
    id: '2',
    email: '<EMAIL>',
    username: 'john_doe',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    bio: 'Frontend developer passionate about React and TypeScript. Always learning new technologies.',
    createdAt: '2023-02-20T14:30:00Z',
    banned: false,
    reputation: 1250,
    questionsCount: 15,
    answersCount: 45,
  },
  {
    id: '3',
    email: '<EMAIL>',
    username: 'jane_smith',
    role: '<PERSON><PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    bio: 'Backend engineer specializing in Node.js and Python. Love solving complex problems.',
    createdAt: '2023-03-10T09:15:00Z',
    banned: false,
    reputation: 2100,
    questionsCount: 8,
    answersCount: 78,
  },
  {
    id: '4',
    email: '<EMAIL>',
    username: 'mike_wilson',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    bio: 'DevOps engineer with expertise in AWS, Docker, and Kubernetes.',
    createdAt: '2023-04-05T16:45:00Z',
    banned: false,
    reputation: 890,
    questionsCount: 12,
    answersCount: 32,
  },
  {
    id: '5',
    email: '<EMAIL>',
    username: 'sarah_johnson',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    bio: 'UI/UX designer who codes. Passionate about creating beautiful and accessible interfaces.',
    createdAt: '2023-05-12T11:20:00Z',
    banned: false,
    reputation: 675,
    questionsCount: 6,
    answersCount: 28,
  },
  {
    id: '6',
    email: '<EMAIL>',
    username: 'alex_chen',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
    bio: 'Mobile app developer working with React Native and Flutter.',
    createdAt: '2023-06-18T13:10:00Z',
    banned: false,
    reputation: 1420,
    questionsCount: 18,
    answersCount: 52,
  },
  {
    id: '7',
    email: '<EMAIL>',
    username: 'emma_davis',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    bio: 'Data scientist and machine learning enthusiast. Python and R expert.',
    createdAt: '2023-07-22T08:30:00Z',
    banned: false,
    reputation: 980,
    questionsCount: 9,
    answersCount: 41,
  },
  {
    id: '8',
    email: '<EMAIL>',
    username: 'david_brown',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    bio: 'Game developer using Unity and C#. Love creating interactive experiences.',
    createdAt: '2023-08-14T15:45:00Z',
    banned: false,
    reputation: 560,
    questionsCount: 11,
    answersCount: 19,
  },
  {
    id: '9',
    email: '<EMAIL>',
    username: 'lisa_garcia',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    bio: 'Cybersecurity specialist with focus on web application security.',
    createdAt: '2023-09-03T12:00:00Z',
    banned: false,
    reputation: 1340,
    questionsCount: 7,
    answersCount: 63,
  },
  {
    id: '10',
    email: '<EMAIL>',
    username: 'tom_anderson',
    role: 'USER',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    bio: 'Blockchain developer working with Ethereum and Solidity.',
    createdAt: '2023-10-11T17:20:00Z',
    banned: false,
    reputation: 720,
    questionsCount: 13,
    answersCount: 25,
  },
];
