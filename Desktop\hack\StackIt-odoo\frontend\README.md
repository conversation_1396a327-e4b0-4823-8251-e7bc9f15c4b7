# StackIt - Q&A Forum Platform

A modern, responsive Q&A forum platform built with React and TypeScript. StackIt provides a collaborative environment for developers and learners to share knowledge, ask questions, and grow together.

![StackIt Screenshot](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=StackIt+Q%26A+Platform)

## 🚀 Features

### Core Functionality
- **Question Management**: Create, edit, and browse questions with rich text formatting
- **Answer System**: Post answers with voting and acceptance functionality
- **Tagging System**: Organize content with autocomplete tags and filtering
- **Search & Filter**: Find questions by keywords, tags, and sorting options
- **User Profiles**: View user statistics and activity history
- **Notification System**: Real-time notifications for interactions

### User Roles
- **Guest Users**: Browse questions and answers in read-only mode
- **Registered Users**: Ask questions, post answers, vote, and receive notifications
- **Admin Users**: Moderate content, manage users, and access platform analytics

### Technical Features
- **Rich Text Editor**: Markdown-based editor with formatting toolbar
- **Responsive Design**: Mobile-first approach with tablet and desktop breakpoints
- **Type Safety**: Full TypeScript implementation with comprehensive interfaces
- **State Management**: Zustand for global state with localStorage persistence
- **Mock Data**: Realistic sample data demonstrating all platform features

## 🛠️ Technology Stack

- **Frontend**: React 18+ with TypeScript
- **Build Tool**: Vite for fast development and building
- **Styling**: Tailwind CSS with custom component library
- **Routing**: React Router v6 for client-side navigation
- **State Management**: Zustand for global state management
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React for consistent iconography
- **Date Handling**: date-fns for date formatting

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/chiragbiradar/StackIt-odoo.git
   cd StackIt-odoo
   git checkout frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to view the application.

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout.tsx      # Main layout wrapper
│   ├── Navbar.tsx      # Navigation component
│   ├── QuestionCard.tsx # Question display component
│   ├── AnswerCard.tsx  # Answer display component
│   ├── RichTextEditor.tsx # Rich text editor
│   ├── TagInput.tsx    # Tag input with autocomplete
│   └── ...
├── pages/              # Page components
│   ├── HomePage.tsx    # Main questions listing
│   ├── QuestionDetailPage.tsx # Individual question view
│   ├── AskQuestionPage.tsx # Question creation form
│   ├── UserProfilePage.tsx # User profile display
│   └── AdminDashboard.tsx # Admin panel
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication context
├── store/              # State management
│   └── useAppStore.ts  # Zustand store
├── data/               # Mock data
│   ├── mockUsers.ts    # Sample users
│   ├── mockQuestions.ts # Sample questions
│   ├── mockAnswers.ts  # Sample answers
│   └── mockTags.ts     # Sample tags
├── types/              # TypeScript type definitions
│   └── index.ts        # All type definitions
├── utils/              # Utility functions
│   └── cn.ts          # Class name utility
└── hooks/              # Custom React hooks
    └── useLocalStorage.ts # localStorage hook
```

## 🎯 Usage

### For Users
1. **Browse Questions**: View all questions on the homepage with filtering options
2. **Ask Questions**: Click "Ask Question" to create a new question with rich text formatting
3. **Answer Questions**: Provide helpful answers with formatting and code examples
4. **Vote on Answers**: Upvote or downvote answers to help highlight the best solutions
5. **Accept Answers**: Question authors can mark the best answer as accepted

### For Admins
1. **Content Moderation**: Review and approve pending questions and answers
2. **User Management**: View user statistics and manage user accounts
3. **Platform Analytics**: Access dashboard with platform statistics and activity

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Adding New Features
1. Create components in the `src/components/` directory
2. Add new pages in the `src/pages/` directory
3. Update types in `src/types/index.ts`
4. Add mock data in the `src/data/` directory
5. Update the store in `src/store/useAppStore.ts`

## 🎨 Design System

The project uses a consistent design system built with Tailwind CSS:

- **Colors**: Primary blue theme with semantic color variants
- **Typography**: Inter font family with responsive text scales
- **Components**: Reusable button, input, card, and badge variants
- **Spacing**: Consistent spacing scale using Tailwind's spacing system
- **Animations**: Subtle animations for better user experience

## 📱 Responsive Design

StackIt is built with a mobile-first approach:
- **Mobile**: Optimized for phones (320px+)
- **Tablet**: Enhanced layout for tablets (768px+)
- **Desktop**: Full-featured experience for desktops (1024px+)

## 🔒 Security Features

- **Input Validation**: Client-side validation with Zod schemas
- **XSS Prevention**: Sanitized HTML content rendering
- **Role-based Access**: Different permissions for user roles
- **Secure Authentication**: Mock authentication with proper session handling

## 🚀 Deployment

The project is configured for easy deployment to various platforms:

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Vercel will automatically detect the Vite configuration
3. Deploy with zero configuration

### Netlify
1. Build the project: `npm run build`
2. Deploy the `dist` folder to Netlify

### GitHub Pages
1. Install gh-pages: `npm install --save-dev gh-pages`
2. Add deploy script to package.json
3. Run: `npm run deploy`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with React and TypeScript
- Styled with Tailwind CSS
- Icons by Lucide React
- Inspired by Stack Overflow and modern Q&A platforms

## 📞 Support

If you have any questions or need help with the project, please:
1. Check the existing issues on GitHub
2. Create a new issue with detailed information
3. Contact the maintainers

---

**StackIt** - Empowering developers to learn and grow together! 🚀
