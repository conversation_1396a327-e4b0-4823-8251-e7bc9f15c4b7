import { create } from 'zustand';
import { Question, Answer, Tag, Notification, QuestionFilters, User } from '@/types';
import { mockQuestions } from '@/data/mockQuestions';
import { mockAnswers } from '@/data/mockAnswers';
import { mockTags } from '@/data/mockTags';
import { mockNotifications } from '@/data/mockNotifications';
import { mockUsers } from '@/data/mockUsers';

interface AppState {
  // Data
  questions: Question[];
  answers: Answer[];
  tags: Tag[];
  notifications: Notification[];
  users: User[];
  
  // UI State
  loading: boolean;
  error: string | null;
  searchQuery: string;
  selectedTags: string[];
  sortBy: 'newest' | 'oldest' | 'most-answered' | 'most-voted';
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSearchQuery: (query: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setSortBy: (sortBy: 'newest' | 'oldest' | 'most-answered' | 'most-voted') => void;
  
  // Question actions
  getQuestions: (filters?: QuestionFilters) => Question[];
  getQuestionById: (id: string) => Question | undefined;
  addQuestion: (question: Omit<Question, 'id' | 'createdAt' | 'updatedAt' | 'viewCount' | 'answerCount' | 'hasAcceptedAnswer'>) => void;
  updateQuestion: (id: string, updates: Partial<Question>) => void;
  deleteQuestion: (id: string) => void;
  incrementQuestionViews: (id: string) => void;
  
  // Answer actions
  getAnswersByQuestionId: (questionId: string) => Answer[];
  addAnswer: (answer: Omit<Answer, 'id' | 'createdAt' | 'updatedAt' | 'upvotes' | 'downvotes' | 'score'>) => void;
  updateAnswer: (id: string, updates: Partial<Answer>) => void;
  deleteAnswer: (id: string) => void;
  voteAnswer: (answerId: string, userId: string, voteType: 'UP' | 'DOWN') => void;
  acceptAnswer: (answerId: string, questionId: string) => void;
  
  // Tag actions
  getPopularTags: (limit?: number) => Tag[];
  searchTags: (query: string) => Tag[];
  addTag: (tag: Omit<Tag, 'id' | 'createdAt' | 'questionCount'>) => void;
  
  // Notification actions
  getNotificationsByUserId: (userId: string) => Notification[];
  getUnreadNotificationCount: (userId: string) => number;
  markNotificationAsRead: (id: string) => void;
  markAllNotificationsAsRead: (userId: string) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  
  // User actions
  getUserById: (id: string) => User | undefined;
  updateUser: (id: string, updates: Partial<User>) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  questions: mockQuestions,
  answers: mockAnswers,
  tags: mockTags,
  notifications: mockNotifications,
  users: mockUsers,
  loading: false,
  error: null,
  searchQuery: '',
  selectedTags: [],
  sortBy: 'newest',

  // UI Actions
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setSearchQuery: (searchQuery) => set({ searchQuery }),
  setSelectedTags: (selectedTags) => set({ selectedTags }),
  setSortBy: (sortBy) => set({ sortBy }),

  // Question actions
  getQuestions: (filters) => {
    const { questions, searchQuery, selectedTags, sortBy } = get();
    let filteredQuestions = [...questions];

    // Apply search filter
    if (filters?.search || searchQuery) {
      const query = (filters?.search || searchQuery).toLowerCase();
      filteredQuestions = filteredQuestions.filter(q =>
        q.title.toLowerCase().includes(query) ||
        q.description.toLowerCase().includes(query)
      );
    }

    // Apply tag filter
    const tagsToFilter = filters?.tags || selectedTags;
    if (tagsToFilter.length > 0) {
      filteredQuestions = filteredQuestions.filter(q =>
        q.tags.some(tag => tagsToFilter.includes(tag.name))
      );
    }

    // Apply status filter
    if (filters?.status) {
      filteredQuestions = filteredQuestions.filter(q => q.status === filters.status);
    }

    // Apply sorting
    const sortOption = filters?.sortBy || sortBy;
    filteredQuestions.sort((a, b) => {
      switch (sortOption) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'most-answered':
          return b.answerCount - a.answerCount;
        case 'most-voted':
          // Calculate total votes for each question's answers
          const aVotes = get().answers
            .filter(ans => ans.questionId === a.id)
            .reduce((sum, ans) => sum + ans.score, 0);
          const bVotes = get().answers
            .filter(ans => ans.questionId === b.id)
            .reduce((sum, ans) => sum + ans.score, 0);
          return bVotes - aVotes;
        default:
          return 0;
      }
    });

    return filteredQuestions;
  },

  getQuestionById: (id) => {
    return get().questions.find(q => q.id === id);
  },

  addQuestion: (questionData) => {
    const newQuestion: Question = {
      ...questionData,
      id: `q_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      viewCount: 0,
      answerCount: 0,
      hasAcceptedAnswer: false,
    };

    set(state => ({
      questions: [newQuestion, ...state.questions]
    }));
  },

  updateQuestion: (id, updates) => {
    set(state => ({
      questions: state.questions.map(q =>
        q.id === id ? { ...q, ...updates, updatedAt: new Date().toISOString() } : q
      )
    }));
  },

  deleteQuestion: (id) => {
    set(state => ({
      questions: state.questions.filter(q => q.id !== id),
      answers: state.answers.filter(a => a.questionId !== id)
    }));
  },

  incrementQuestionViews: (id) => {
    set(state => ({
      questions: state.questions.map(q =>
        q.id === id ? { ...q, viewCount: q.viewCount + 1 } : q
      )
    }));
  },

  // Answer actions
  getAnswersByQuestionId: (questionId) => {
    return get().answers
      .filter(a => a.questionId === questionId)
      .sort((a, b) => {
        // Accepted answers first, then by score, then by date
        if (a.isAccepted && !b.isAccepted) return -1;
        if (!a.isAccepted && b.isAccepted) return 1;
        if (a.score !== b.score) return b.score - a.score;
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });
  },

  addAnswer: (answerData) => {
    const newAnswer: Answer = {
      ...answerData,
      id: `a_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      upvotes: 0,
      downvotes: 0,
      score: 0,
    };

    set(state => ({
      answers: [...state.answers, newAnswer],
      questions: state.questions.map(q =>
        q.id === answerData.questionId
          ? { ...q, answerCount: q.answerCount + 1 }
          : q
      )
    }));
  },

  updateAnswer: (id, updates) => {
    set(state => ({
      answers: state.answers.map(a =>
        a.id === id ? { ...a, ...updates, updatedAt: new Date().toISOString() } : a
      )
    }));
  },

  deleteAnswer: (id) => {
    const answer = get().answers.find(a => a.id === id);
    if (!answer) return;

    set(state => ({
      answers: state.answers.filter(a => a.id !== id),
      questions: state.questions.map(q =>
        q.id === answer.questionId
          ? { 
              ...q, 
              answerCount: Math.max(0, q.answerCount - 1),
              hasAcceptedAnswer: answer.isAccepted ? false : q.hasAcceptedAnswer
            }
          : q
      )
    }));
  },

  voteAnswer: (answerId, userId, voteType) => {
    // This is a simplified voting system for demo purposes
    set(state => ({
      answers: state.answers.map(a => {
        if (a.id === answerId) {
          const currentVote = a.userVote;
          let upvotes = a.upvotes;
          let downvotes = a.downvotes;

          // Remove previous vote if exists
          if (currentVote === 'UP') upvotes--;
          if (currentVote === 'DOWN') downvotes--;

          // Add new vote if different from current
          if (currentVote !== voteType) {
            if (voteType === 'UP') upvotes++;
            if (voteType === 'DOWN') downvotes++;
          }

          return {
            ...a,
            upvotes,
            downvotes,
            score: upvotes - downvotes,
            userVote: currentVote === voteType ? undefined : voteType,
          };
        }
        return a;
      })
    }));
  },

  acceptAnswer: (answerId, questionId) => {
    set(state => ({
      answers: state.answers.map(a => ({
        ...a,
        isAccepted: a.questionId === questionId ? a.id === answerId : a.isAccepted
      })),
      questions: state.questions.map(q =>
        q.id === questionId ? { ...q, hasAcceptedAnswer: true } : q
      )
    }));
  },

  // Tag actions
  getPopularTags: (limit = 10) => {
    return get().tags
      .sort((a, b) => b.questionCount - a.questionCount)
      .slice(0, limit);
  },

  searchTags: (query) => {
    return get().tags.filter(tag =>
      tag.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  addTag: (tagData) => {
    const newTag: Tag = {
      ...tagData,
      id: `t_${Date.now()}`,
      createdAt: new Date().toISOString(),
      questionCount: 0,
    };

    set(state => ({
      tags: [...state.tags, newTag]
    }));
  },

  // Notification actions
  getNotificationsByUserId: (userId) => {
    return get().notifications
      .filter(n => n.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  },

  getUnreadNotificationCount: (userId) => {
    return get().notifications.filter(n => n.userId === userId && !n.read).length;
  },

  markNotificationAsRead: (id) => {
    set(state => ({
      notifications: state.notifications.map(n =>
        n.id === id ? { ...n, read: true } : n
      )
    }));
  },

  markAllNotificationsAsRead: (userId) => {
    set(state => ({
      notifications: state.notifications.map(n =>
        n.userId === userId ? { ...n, read: true } : n
      )
    }));
  },

  addNotification: (notificationData) => {
    const newNotification: Notification = {
      ...notificationData,
      id: `n_${Date.now()}`,
      createdAt: new Date().toISOString(),
    };

    set(state => ({
      notifications: [newNotification, ...state.notifications]
    }));
  },

  // User actions
  getUserById: (id) => {
    return get().users.find(u => u.id === id);
  },

  updateUser: (id, updates) => {
    set(state => ({
      users: state.users.map(u =>
        u.id === id ? { ...u, ...updates } : u
      )
    }));
  },
}));
