{"name": "stackit", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "zustand": "^4.4.7", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-text-align": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-character-count": "^2.1.13"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}