/**
 * Questions Store - API-based state management for questions
 */

import { create } from 'zustand';
import { Question, QuestionFilters } from '@/types';
import { questionsA<PERSON>, Api<PERSON>rror, QuestionListResponse } from '@/utils';

interface QuestionsState {
  // Data
  questions: Question[];
  currentQuestion: Question | null;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalQuestions: number;
  hasNext: boolean;
  hasPrev: boolean;
  
  // UI State
  loading: boolean;
  error: string | null;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Question actions
  fetchQuestions: (filters?: QuestionFilters) => Promise<void>;
  fetchQuestionById: (id: number) => Promise<Question | null>;
  createQuestion: (questionData: { title: string; description: string; tag_names: string[] }) => Promise<Question | null>;
  clearCurrentQuestion: () => void;
  
  // Utility actions
  reset: () => void;
}

// Helper function to convert backend question to frontend format
const convertBackendQuestion = (backendQuestion: any): Question => {
  return {
    id: backendQuestion.id,
    title: backendQuestion.title,
    description: backendQuestion.description,
    author_id: backendQuestion.author?.id,
    author: {
      id: backendQuestion.author.id,
      username: backendQuestion.author.username,
      email: backendQuestion.author.email || '',
      full_name: backendQuestion.author.full_name,
      bio: backendQuestion.author.bio,
      role: 'USER',
      is_active: true,
      is_verified: false,
      reputation_score: backendQuestion.author.reputation_score,
      questions_count: 0,
      answers_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    tags: backendQuestion.tags.map((tag: any) => ({
      id: tag.id,
      name: tag.name,
      description: tag.description,
      color: tag.color,
      usage_count: 0,
      created_at: new Date().toISOString(),
    })),
    created_at: backendQuestion.created_at,
    updated_at: backendQuestion.updated_at,
    view_count: backendQuestion.view_count,
    answer_count: backendQuestion.answer_count,
    has_accepted_answer: backendQuestion.has_accepted_answer,
    vote_score: backendQuestion.vote_score || 0,
    is_closed: backendQuestion.is_closed || false,
  };
};

export const useQuestionsStore = create<QuestionsState>((set, get) => ({
  // Initial state
  questions: [],
  currentQuestion: null,
  currentPage: 1,
  totalPages: 1,
  totalQuestions: 0,
  hasNext: false,
  hasPrev: false,
  loading: false,
  error: null,

  // UI Actions
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),

  // Question actions
  fetchQuestions: async (filters = {}) => {
    set({ loading: true, error: null });
    
    try {
      const response: QuestionListResponse = await questionsApi.getQuestions(filters);
      
      const questions = response.questions.map(convertBackendQuestion);
      
      set({
        questions,
        currentPage: response.page,
        totalQuestions: response.total,
        hasNext: response.has_next,
        hasPrev: response.has_prev,
        totalPages: Math.ceil(response.total / (filters.per_page || 10)),
        loading: false,
      });
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to fetch questions',
        loading: false,
      });
    }
  },

  fetchQuestionById: async (id: number) => {
    set({ loading: true, error: null });
    
    try {
      const response = await questionsApi.getQuestionById(id);
      const question = convertBackendQuestion(response);
      
      set({
        currentQuestion: question,
        loading: false,
      });
      
      return question;
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to fetch question',
        loading: false,
        currentQuestion: null,
      });
      return null;
    }
  },

  createQuestion: async (questionData) => {
    set({ loading: true, error: null });
    
    try {
      const response = await questionsApi.createQuestion(questionData);
      const question = convertBackendQuestion(response);
      
      // Add the new question to the beginning of the list
      set(state => ({
        questions: [question, ...state.questions],
        loading: false,
      }));
      
      return question;
    } catch (error) {
      const apiError = error as ApiError;
      set({
        error: apiError.detail || 'Failed to create question',
        loading: false,
      });
      return null;
    }
  },

  clearCurrentQuestion: () => set({ currentQuestion: null }),

  reset: () => set({
    questions: [],
    currentQuestion: null,
    currentPage: 1,
    totalPages: 1,
    totalQuestions: 0,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null,
  }),
}));
