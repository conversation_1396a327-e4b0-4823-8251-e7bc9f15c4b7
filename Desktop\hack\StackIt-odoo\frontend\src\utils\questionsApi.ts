/**
 * Questions API Service
 * Handles all question-related API calls
 */

import { apiClient } from './api';

// Types for question requests and responses
export interface QuestionCreateRequest {
  title: string;
  description: string;
  tag_names: string[];
}

export interface AuthorInfo {
  id: number;
  username: string;
  full_name?: string;
  reputation_score: number;
}

export interface TagInfo {
  id: number;
  name: string;
  color?: string;
}

export interface QuestionResponse {
  id: number;
  title: string;
  description: string;
  view_count: number;
  vote_score: number;
  answer_count: number;
  is_closed: boolean;
  has_accepted_answer: boolean;
  author: AuthorInfo;
  tags: TagInfo[];
  created_at: string;
  updated_at: string;
}

export interface QuestionListItem {
  id: number;
  title: string;
  description: string;
  view_count: number;
  vote_score: number;
  answer_count: number;
  has_accepted_answer: boolean;
  author: AuthorInfo;
  tags: TagInfo[];
  created_at: string;
}

export interface QuestionListResponse {
  questions: QuestionListItem[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface QuestionFilters {
  page?: number;
  per_page?: number;
  search?: string;
  tags?: string[];
  sort_by?: 'newest' | 'oldest' | 'most-answered' | 'most-voted';
}

// Questions API functions
export const questionsApi = {
  /**
   * Get list of questions with pagination and filters
   */
  async getQuestions(filters: QuestionFilters = {}): Promise<QuestionListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.per_page) params.append('per_page', filters.per_page.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    
    const queryString = params.toString();
    const endpoint = queryString ? `/questions?${queryString}` : '/questions';
    
    return apiClient.get<QuestionListResponse>(endpoint);
  },

  /**
   * Get a specific question by ID
   */
  async getQuestionById(id: number): Promise<QuestionResponse> {
    return apiClient.get<QuestionResponse>(`/questions/${id}`);
  },

  /**
   * Create a new question
   */
  async createQuestion(questionData: QuestionCreateRequest): Promise<QuestionResponse> {
    return apiClient.post<QuestionResponse>('/questions', questionData);
  },

  /**
   * Update a question (if needed in the future)
   */
  async updateQuestion(id: number, updates: Partial<QuestionCreateRequest>): Promise<QuestionResponse> {
    return apiClient.put<QuestionResponse>(`/questions/${id}`, updates);
  },

  /**
   * Delete a question (if needed in the future)
   */
  async deleteQuestion(id: number): Promise<void> {
    return apiClient.delete<void>(`/questions/${id}`);
  },
};
