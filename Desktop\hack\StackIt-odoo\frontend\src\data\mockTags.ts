import { Tag } from '@/types';

export const mockTags: Tag[] = [
  {
    id: '1',
    name: 'react',
    description: 'A JavaScript library for building user interfaces',
    color: '#61DAFB',
    questionCount: 45,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'typescript',
    description: 'A typed superset of JavaScript that compiles to plain JavaScript',
    color: '#3178C6',
    questionCount: 38,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '3',
    name: 'javascript',
    description: 'A high-level, interpreted programming language',
    color: '#F7DF1E',
    questionCount: 52,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '4',
    name: 'node.js',
    description: 'A JavaScript runtime built on Chrome\'s V8 JavaScript engine',
    color: '#339933',
    questionCount: 29,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '5',
    name: 'python',
    description: 'A high-level, general-purpose programming language',
    color: '#3776AB',
    questionCount: 34,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '6',
    name: 'css',
    description: 'A style sheet language used for describing the presentation of a document',
    color: '#1572B6',
    questionCount: 26,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '7',
    name: 'html',
    description: 'The standard markup language for documents designed to be displayed in a web browser',
    color: '#E34F26',
    questionCount: 18,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '8',
    name: 'vue.js',
    description: 'A progressive JavaScript framework for building user interfaces',
    color: '#4FC08D',
    questionCount: 22,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '9',
    name: 'angular',
    description: 'A TypeScript-based open-source web application framework',
    color: '#DD0031',
    questionCount: 19,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '10',
    name: 'express',
    description: 'A minimal and flexible Node.js web application framework',
    color: '#000000',
    questionCount: 15,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '11',
    name: 'mongodb',
    description: 'A document-oriented NoSQL database program',
    color: '#47A248',
    questionCount: 21,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '12',
    name: 'postgresql',
    description: 'An open-source relational database management system',
    color: '#336791',
    questionCount: 17,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '13',
    name: 'docker',
    description: 'A platform for developing, shipping, and running applications in containers',
    color: '#2496ED',
    questionCount: 13,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '14',
    name: 'aws',
    description: 'Amazon Web Services cloud computing platform',
    color: '#FF9900',
    questionCount: 16,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '15',
    name: 'git',
    description: 'A distributed version control system',
    color: '#F05032',
    questionCount: 12,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '16',
    name: 'tailwindcss',
    description: 'A utility-first CSS framework',
    color: '#06B6D4',
    questionCount: 14,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '17',
    name: 'next.js',
    description: 'A React framework for production',
    color: '#000000',
    questionCount: 25,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '18',
    name: 'graphql',
    description: 'A query language for APIs and a runtime for fulfilling those queries',
    color: '#E10098',
    questionCount: 11,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '19',
    name: 'redux',
    description: 'A predictable state container for JavaScript apps',
    color: '#764ABC',
    questionCount: 20,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '20',
    name: 'testing',
    description: 'Software testing methodologies and tools',
    color: '#8DD6F9',
    questionCount: 9,
    createdAt: '2023-01-15T10:00:00Z',
  },
];
