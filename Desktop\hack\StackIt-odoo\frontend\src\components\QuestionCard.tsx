import React from 'react';
import { Link } from 'react-router-dom';
import { MessageCircle, Eye, Clock, CheckCircle } from 'lucide-react';
import { QuestionCardProps } from '@/types';
import { formatDistanceToNow } from 'date-fns';

export function QuestionCard({ question, showActions = true }: QuestionCardProps) {
  const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="card-hover p-6">
      <div className="flex items-start space-x-4">
        {/* Stats */}
        <div className="flex flex-col items-center space-y-2 text-sm text-gray-500 min-w-[60px]">
          <div className="flex items-center space-x-1">
            <MessageCircle className="w-4 h-4" />
            <span>{question.answerCount}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{question.viewCount}</span>
          </div>
          {question.hasAcceptedAnswer && (
            <CheckCircle className="w-4 h-4 text-green-500" />
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <Link
              to={`/questions/${question.id}`}
              className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors line-clamp-2"
            >
              {question.title}
            </Link>
            <span className={`badge ml-2 flex-shrink-0 ${
              question.status === 'APPROVED' ? 'badge-success' :
              question.status === 'PENDING' ? 'badge-warning' :
              'badge-danger'
            }`}>
              {question.status.toLowerCase()}
            </span>
          </div>

          <p className="text-gray-600 mb-4 line-clamp-3">
            {truncateText(stripHtml(question.description))}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            {question.tags.map((tag) => (
              <Link
                key={tag.id}
                to={`/?tag=${encodeURIComponent(tag.name)}`}
                className="badge-primary hover:bg-primary-200 transition-colors"
                style={{ backgroundColor: tag.color ? `${tag.color}20` : undefined }}
              >
                {tag.name}
              </Link>
            ))}
          </div>

          {/* Meta */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>
                  asked {formatDistanceToNow(new Date(question.createdAt), { addSuffix: true })}
                </span>
              </div>
              <Link
                to={`/users/${question.author.username}`}
                className="flex items-center space-x-2 hover:text-gray-700 transition-colors"
              >
                {question.author.avatar ? (
                  <img
                    src={question.author.avatar}
                    alt={question.author.username}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-6 h-6 bg-gray-300 rounded-full" />
                )}
                <span>{question.author.username}</span>
                {question.author.role === 'ADMIN' && (
                  <span className="badge-primary text-xs">Admin</span>
                )}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
