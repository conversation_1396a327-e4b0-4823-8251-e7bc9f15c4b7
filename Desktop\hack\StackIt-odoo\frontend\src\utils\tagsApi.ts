/**
 * Tags API Service
 * Handles all tag-related API calls
 */

import { apiClient } from './api';

// Types for tag requests and responses
export interface TagResponse {
  id: number;
  name: string;
  description?: string;
  color?: string;
  usage_count: number;
}

export interface TagListResponse {
  tags: TagResponse[];
  total: number;
}

// Tags API functions
export const tagsApi = {
  /**
   * Get list of all tags ordered by usage count
   */
  async getTags(): Promise<TagListResponse> {
    return apiClient.get<TagListResponse>('/tags');
  },

  /**
   * Search tags by name (client-side filtering for now)
   */
  async searchTags(query: string): Promise<TagResponse[]> {
    const response = await this.getTags();
    return response.tags.filter(tag => 
      tag.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  /**
   * Get popular tags (top N by usage count)
   */
  async getPopularTags(limit: number = 10): Promise<TagResponse[]> {
    const response = await this.getTags();
    return response.tags.slice(0, limit);
  },
};
