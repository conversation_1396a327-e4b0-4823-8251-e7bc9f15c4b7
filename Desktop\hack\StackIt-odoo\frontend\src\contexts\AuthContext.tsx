import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthContextType, RegisterForm } from '@/types';
import { authApi, ApiError } from '@/utils';

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  loading: true,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, loading: false, error: null };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'LOGOUT':
      return { ...state, user: null, loading: false, error: null };
    default:
      return state;
  }
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for existing authentication
    const checkAuth = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });

      try {
        // Check if user is authenticated and get current user info
        if (authApi.isAuthenticated()) {
          const currentUser = await authApi.getCurrentUser();

          // Convert backend user format to frontend format
          const user: User = {
            id: currentUser.id,
            username: currentUser.username,
            email: currentUser.email,
            full_name: currentUser.full_name,
            bio: currentUser.bio,
            role: currentUser.role as 'USER' | 'ADMIN',
            is_active: currentUser.is_active,
            is_verified: currentUser.is_verified,
            reputation_score: currentUser.reputation_score,
            questions_count: currentUser.questions_count,
            answers_count: currentUser.answers_count,
            created_at: currentUser.created_at,
            updated_at: currentUser.updated_at,
          };

          dispatch({ type: 'SET_USER', payload: user });
        } else {
          dispatch({ type: 'SET_USER', payload: null });
        }
      } catch (error) {
        // Token might be expired or invalid
        authApi.logout();
        dispatch({ type: 'SET_USER', payload: null });
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await authApi.login({ username, password });

      // Convert backend user format to frontend format
      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email,
        full_name: response.user.full_name,
        bio: response.user.bio,
        role: response.user.role as 'USER' | 'ADMIN',
        is_active: response.user.is_active,
        is_verified: response.user.is_verified,
        reputation_score: response.user.reputation_score,
        questions_count: response.user.questions_count,
        answers_count: response.user.answers_count,
        created_at: response.user.created_at,
        updated_at: response.user.updated_at,
      };

      dispatch({ type: 'SET_USER', payload: user });
      return true;
    } catch (error) {
      const apiError = error as ApiError;
      dispatch({ type: 'SET_ERROR', payload: apiError.detail || 'Login failed' });
      return false;
    }
  };

  const register = async (data: RegisterForm): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await authApi.register({
        username: data.username,
        email: data.email,
        password: data.password,
        full_name: data.full_name,
        bio: data.bio,
      });

      // Convert backend user format to frontend format
      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email,
        full_name: response.user.full_name,
        bio: response.user.bio,
        role: response.user.role as 'USER' | 'ADMIN',
        is_active: response.user.is_active,
        is_verified: response.user.is_verified,
        reputation_score: response.user.reputation_score,
        questions_count: response.user.questions_count,
        answers_count: response.user.answers_count,
        created_at: response.user.created_at,
        updated_at: response.user.updated_at,
      };

      dispatch({ type: 'SET_USER', payload: user });
      return true;
    } catch (error) {
      const apiError = error as ApiError;
      dispatch({ type: 'SET_ERROR', payload: apiError.detail || 'Registration failed' });
      return false;
    }
  };

  const logout = () => {
    authApi.logout();
    dispatch({ type: 'LOGOUT' });
  };

  const value: AuthContextType = {
    user: state.user,
    login,
    register,
    logout,
    isAuthenticated: !!state.user,
    isAdmin: state.user?.role === 'ADMIN',
    loading: state.loading,
    error: state.error,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
